# Cube1_Group 团队编码规范

## 🎯 规范目标

建立统一的编码标准，确保代码质量、可维护性和团队协作效率。基于项目实际情况和最佳实践制定。

## 📁 1. 项目结构规范

### 1.1 目录组织

```
cube1_group/
├── components/          # React组件 - 按功能域分组
│   ├── grid/           # 网格相关组件
│   ├── panels/         # 控制面板组件  
│   └── common/         # 通用组件
├── stores/             # Zustand状态管理 - 按业务域分离
│   ├── basicDataStore.ts      # 核心数据管理
│   ├── businessDataStore.ts   # 业务逻辑管理
│   └── styleStore.ts          # 样式配置管理
├── hooks/              # 自定义Hook - 性能优化核心
├── utils/              # 工具函数 - 纯函数设计
├── constants/          # 常量管理 - 分离架构
│   ├── colors.ts       # 颜色常量
│   └── styles.ts       # 样式常量
├── types/              # TypeScript类型定义
└── __tests__/          # 测试文件
```

### 1.2 文件命名规范

| 类型 | 命名规则 | 示例 |
|------|----------|------|
| 组件文件 | PascalCase.tsx | `GridContainer.tsx` |
| Hook文件 | camelCase.ts | `useColorData.ts` |
| 工具文件 | camelCase.ts | `colorUtils.ts` |
| 常量文件 | camelCase.ts | `colors.ts` |
| 类型文件 | camelCase.ts | `gridTypes.ts` |
| 测试文件 | *.test.ts(x) | `GridCell.test.tsx` |

## 🏗️ 2. 组件开发规范

### 2.1 组件结构模板

```typescript
/**
 * 组件名称 - 简短描述
 * 🎯 职责：明确的单一职责描述
 * 📦 依赖：主要依赖说明
 * ✅ 优化：性能优化措施说明
 */

import React, { memo, useMemo, useCallback } from 'react';
import { cn } from '@/lib/utils';

// 1. 类型定义
interface ComponentProps {
  // 必需属性
  id: string;
  title: string;
  
  // 可选属性
  className?: string;
  disabled?: boolean;
  
  // 事件处理
  onClick?: (id: string) => void;
}

// 2. 组件实现
const Component: React.FC<ComponentProps> = memo(({
  id,
  title,
  className,
  disabled = false,
  onClick
}) => {
  // 3. 计算属性 (useMemo)
  const computedStyle = useMemo(() => {
    return cn(
      'base-classes',
      disabled && 'disabled-classes',
      className
    );
  }, [disabled, className]);

  // 4. 事件处理 (useCallback)
  const handleClick = useCallback(() => {
    if (!disabled && onClick) {
      onClick(id);
    }
  }, [id, disabled, onClick]);

  // 5. 渲染
  return (
    <div className={computedStyle} onClick={handleClick}>
      {title}
    </div>
  );
});

// 6. 显示名称
Component.displayName = 'Component';

export default Component;
```

### 2.2 组件开发原则

**必须遵循**:
- ✅ 使用 `React.memo` 包装所有组件
- ✅ 计算属性使用 `useMemo`
- ✅ 事件处理使用 `useCallback`
- ✅ 避免内联对象和函数
- ✅ 组件文件 <500行，函数 <100行

**禁止使用**:
- ❌ `useState` (使用Zustand替代)
- ❌ 内联样式对象
- ❌ 匿名函数作为props
- ❌ 直接修改props

## 🏪 3. 状态管理规范

### 3.1 Zustand Store结构

```typescript
/**
 * Store名称 - 业务域描述
 * 🎯 职责：明确的业务职责
 * 📊 数据：管理的数据类型
 * 🔄 操作：提供的操作方法
 */

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

// 1. 状态接口定义
interface StoreState {
  // 数据状态
  data: DataType;
  loading: boolean;
  error: string | null;
  
  // 操作方法
  setData: (data: DataType) => void;
  updateData: (id: string, updates: Partial<DataType>) => void;
  resetData: () => void;
}

// 2. 初始状态
const initialState = {
  data: {},
  loading: false,
  error: null,
};

// 3. Store创建
export const useStore = create<StoreState>()(
  devtools(
    (set, get) => ({
      ...initialState,
      
      // 同步操作
      setData: (data) => set({ data }, false, 'setData'),
      
      // 异步操作
      updateData: async (id, updates) => {
        set({ loading: true }, false, 'updateData/start');
        try {
          const result = await updateDataAPI(id, updates);
          set({ 
            data: { ...get().data, [id]: result },
            loading: false 
          }, false, 'updateData/success');
        } catch (error) {
          set({ 
            error: error.message, 
            loading: false 
          }, false, 'updateData/error');
        }
      },
      
      // 重置操作
      resetData: () => set(initialState, false, 'resetData'),
    }),
    { name: 'store-name' }
  )
);
```

### 3.2 Store使用规范

**选择器优化**:
```typescript
// ✅ 正确：使用选择器避免不必要的重渲染
const data = useStore(state => state.data);
const loading = useStore(state => state.loading);

// ❌ 错误：选择整个state导致过度渲染
const state = useStore();
```

**批量更新**:
```typescript
// ✅ 正确：批量更新减少渲染次数
set(state => ({
  data: newData,
  loading: false,
  error: null
}), false, 'batchUpdate');

// ❌ 错误：多次单独更新
set({ data: newData });
set({ loading: false });
set({ error: null });
```

## 🎨 4. 样式规范

### 4.1 Tailwind CSS使用规范

**类名组织**:
```typescript
// ✅ 正确：使用cn工具函数组合类名
const className = cn(
  // 基础样式
  'flex items-center justify-center',
  // 尺寸样式
  'w-full h-10 px-4 py-2',
  // 颜色样式
  'bg-blue-600 text-white',
  // 交互样式
  'hover:bg-blue-700 focus:ring-2 focus:ring-blue-500',
  // 条件样式
  disabled && 'opacity-50 cursor-not-allowed',
  // 自定义样式
  className
);

// ❌ 错误：长字符串拼接
const className = `flex items-center justify-center w-full h-10 px-4 py-2 bg-blue-600 text-white hover:bg-blue-700 ${disabled ? 'opacity-50' : ''}`;
```

**常量使用**:
```typescript
// ✅ 正确：使用预定义常量
import { BUTTON_STYLES, SIZE_STYLES } from '@/constants/styles';

const buttonClass = cn(
  BUTTON_STYLES.primary,
  SIZE_STYLES.md
);

// ❌ 错误：硬编码样式
const buttonClass = 'bg-blue-600 text-white px-4 py-2';
```

### 4.2 样式常量规范

**按钮样式系统**:
```typescript
// 变体定义
export type ButtonVariant = 'primary' | 'secondary' | 'danger' | 'success';
export type ButtonSize = 'xs' | 'sm' | 'md' | 'lg';

// 尺寸映射
export const SIZE_STYLES: Record<ButtonSize, string> = {
  xs: 'px-2 py-1 text-xs',
  sm: 'px-3 py-1.5 text-sm', 
  md: 'px-4 py-2 text-sm',
  lg: 'px-6 py-3 text-base',
};

// 变体映射
export const VARIANT_STYLES: Record<ButtonVariant, string> = {
  primary: 'bg-blue-600 text-white hover:bg-blue-700',
  secondary: 'bg-gray-200 text-gray-700 hover:bg-gray-300',
  danger: 'bg-red-600 text-white hover:bg-red-700',
  success: 'bg-green-600 text-white hover:bg-green-700',
};
```

## 🔧 5. 工具函数规范

### 5.1 纯函数设计

```typescript
/**
 * 函数描述 - 简短说明
 * @param param1 参数1描述
 * @param param2 参数2描述  
 * @returns 返回值描述
 * @example
 * ```typescript
 * const result = functionName(arg1, arg2);
 * ```
 */
export const functionName = (
  param1: Type1,
  param2: Type2
): ReturnType => {
  // 参数验证
  if (!param1 || !param2) {
    throw new Error('Invalid parameters');
  }
  
  // 业务逻辑
  const result = processData(param1, param2);
  
  // 返回结果
  return result;
};
```

### 5.2 工具函数分类

**按功能域组织**:
```
utils/
├── colorUtils.ts       # 颜色处理工具
├── buttonUtils.ts      # 按钮样式工具
├── styleUtils.ts       # 通用样式工具
├── dataUtils.ts        # 数据处理工具
├── validationUtils.ts  # 验证工具
└── formatUtils.ts      # 格式化工具
```

## 📝 6. 命名规范

### 6.1 变量命名

| 类型 | 规则 | 示例 |
|------|------|------|
| 变量 | camelCase | `colorData`, `isLoading` |
| 常量 | UPPER_SNAKE_CASE | `GRID_SIZE`, `DEFAULT_COLOR` |
| 函数 | camelCase | `getCellStyle`, `updateData` |
| 类 | PascalCase | `DataProcessor`, `ColorManager` |
| 接口 | PascalCase | `GridProps`, `ColorType` |
| 类型 | PascalCase | `ButtonVariant`, `StoreState` |

### 6.2 布尔值命名

```typescript
// ✅ 正确：使用is/has/can/should前缀
const isLoading = true;
const hasError = false;
const canEdit = true;
const shouldUpdate = false;

// ❌ 错误：模糊的布尔值命名
const loading = true;
const error = false;
const edit = true;
```

### 6.3 事件处理命名

```typescript
// ✅ 正确：handle前缀 + 动作
const handleClick = () => {};
const handleSubmit = () => {};
const handleChange = () => {};

// ✅ 正确：on前缀用于props
interface Props {
  onClick?: () => void;
  onSubmit?: () => void;
  onChange?: () => void;
}
```

## 🧪 7. 测试规范

### 7.1 测试文件组织

```
__tests__/
├── components/
│   └── GridCell.test.tsx
├── stores/
│   └── basicDataStore.test.ts
├── utils/
│   └── colorUtils.test.ts
└── integration/
    └── userFlow.test.ts
```

### 7.2 测试用例结构

```typescript
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, beforeEach } from '@jest/globals';
import Component from '../Component';

describe('Component', () => {
  // 测试分组
  describe('rendering', () => {
    it('should render with default props', () => {
      render(<Component id="test" title="Test" />);
      expect(screen.getByText('Test')).toBeInTheDocument();
    });
  });

  describe('interactions', () => {
    it('should handle click events', () => {
      const handleClick = jest.fn();
      render(<Component id="test" title="Test" onClick={handleClick} />);
      
      fireEvent.click(screen.getByText('Test'));
      expect(handleClick).toHaveBeenCalledWith('test');
    });
  });

  describe('edge cases', () => {
    it('should handle disabled state', () => {
      render(<Component id="test" title="Test" disabled />);
      expect(screen.getByText('Test')).toHaveClass('disabled');
    });
  });
});
```

## 🔍 8. 代码审查清单

### 8.1 功能检查
- [ ] 功能实现正确且完整
- [ ] 边界条件处理妥当
- [ ] 错误处理机制完善
- [ ] 性能考虑充分

### 8.2 代码质量
- [ ] 代码结构清晰合理
- [ ] 命名规范且有意义
- [ ] 注释充分且准确
- [ ] 无重复代码

### 8.3 技术规范
- [ ] TypeScript类型定义完整
- [ ] React最佳实践遵循
- [ ] 性能优化措施到位
- [ ] 测试覆盖充分

### 8.4 团队协作
- [ ] 符合项目编码规范
- [ ] 文档更新及时
- [ ] 向后兼容性考虑
- [ ] 可维护性良好

## 🛠️ 9. 开发工具配置

### 9.1 ESLint配置

```json
{
  "extends": [
    "next/core-web-vitals",
    "@typescript-eslint/recommended",
    "prettier"
  ],
  "rules": {
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/explicit-function-return-type": "warn",
    "react-hooks/exhaustive-deps": "error",
    "react/display-name": "error"
  }
}
```

### 9.2 Prettier配置

```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false
}
```

### 9.3 Git Hooks

```json
{
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged",
      "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"
    }
  },
  "lint-staged": {
    "*.{ts,tsx}": [
      "eslint --fix",
      "prettier --write",
      "git add"
    ]
  }
}
```

## 📊 10. 质量指标

### 10.1 代码质量指标

| 指标 | 目标值 | 检查方式 |
|------|--------|----------|
| 测试覆盖率 | >80% | Jest coverage |
| 类型覆盖率 | >95% | TypeScript strict |
| ESLint通过率 | 100% | CI检查 |
| 代码重复率 | <5% | SonarQube |

### 10.2 性能指标

| 指标 | 目标值 | 检查方式 |
|------|--------|----------|
| 包大小 | <1.5MB | Bundle analyzer |
| 首屏加载 | <800ms | Lighthouse |
| 交互响应 | <100ms | Performance API |
| 内存使用 | <50MB | DevTools |

---

**规范版本**: v1.0  
**制定日期**: 2025-06-30  
**适用范围**: Cube1_Group项目全体开发人员  
**更新频率**: 每季度评估和更新一次
